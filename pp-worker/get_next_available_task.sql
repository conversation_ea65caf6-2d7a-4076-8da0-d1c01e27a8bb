-- PostgreSQL function to get the next available task with SKIP LOCKED
-- This prevents race conditions by skipping tasks that are already locked by other workers

CREATE OR REPLACE FUNCTION get_next_available_task(
    p_table_name TEXT DEFAULT 'tasks',
    p_worker_id TEXT DEFAULT NULL
) RETURNS TABLE (
    id INT,
    status TEXT,
    job_json JSONB,
    vars JSONB,
    workervars JSON<PERSON>,
    result JSONB,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    job_id INT
) AS $$
DECLARE
    query TEXT;
    task_record RECORD;
BEGIN
    -- Dynamically construct the query to get and lock the next available task
    query := format('
        SELECT * FROM public.%I 
        WHERE status = ''queued'' 
        ORDER BY created_at ASC 
        LIMIT 1 
        FOR UPDATE SKIP LOCKED
    ', p_table_name);
    
    -- Execute the query and get the first available task
    FOR task_record IN EXECUTE query LOOP
        -- Update the task status to 'In Progress' immediately
        EXECUTE format('
            UPDATE public.%I 
            SET status = ''In Progress'', 
                updated_at = CURRENT_TIMESTAMP 
            WHERE id = $1
        ', p_table_name) USING task_record.id;
        
        -- Return the task data
        id := task_record.id;
        status := 'In Progress';
        job_json := task_record.job_json;
        vars := task_record.vars;
        workervars := task_record.workervars;
        result := task_record.result;
        created_at := task_record.created_at;
        updated_at := CURRENT_TIMESTAMP;
        job_id := task_record.job_id;
        
        RETURN NEXT;
        RETURN; -- Exit after finding one task
    END LOOP;
    
    -- If no task was found, return empty result
    RETURN;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION get_next_available_task(TEXT, TEXT) TO authenticated;

-- Add comment for documentation
COMMENT ON FUNCTION get_next_available_task(TEXT, TEXT) IS 'Gets the next available queued task and immediately locks it to prevent race conditions';
