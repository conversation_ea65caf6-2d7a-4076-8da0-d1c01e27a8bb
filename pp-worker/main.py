import logging
from concurrent.futures import <PERSON><PERSON>oolExecutor, as_completed
import signal
import sys
import time
import threading
import concurrent.futures
from multiprocessing import Event, current_process

import config
import database
from job_processing import TaskProcessor # Renamed JobProcessor -> TaskProcessor

# Initialize logging
logging.basicConfig(
    level=logging.INFO, 
    format='[%(asctime)s] %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logging.getLogger("httpx").setLevel(logging.WARNING)
logging.getLogger("supabase").setLevel(logging.WARNING)

# Initialize counters and lock
active_workers = 0
active_workers_lock = threading.Lock()
total_workers = config.max_processes

# Initialize shutdown event
shutdown_event = Event()

def signal_handler(sig, frame):
    logging.info("Shutdown signal received. Initiating graceful shutdown...")
    shutdown_event.set()

def run_task(task_data): # Renamed run_job -> run_task, job -> task_data
    """Function to run a task using TaskProcessor.""" # Updated docstring
    if shutdown_event.is_set():
        logging.info(f"Shutdown signal set, skipping task {task_data.get('id', 'UNKNOWN')}") # Added log
        return
    processor = TaskProcessor() # Renamed JobProcessor -> TaskProcessor
    processor.process_task(task_data) # Renamed process_job -> process_task

def task_done_callback(future, task_id): # Renamed job_done_callback -> task_done_callback, job_id -> task_id
    """Callback function to handle task completion.""" # Updated docstring
    global active_workers
    try:
        # Retrieve the result to catch any exceptions raised during task execution
        future.result()
        # Only log completion for tasks that were actually processed
        # (Tasks that couldn't be locked will not generate detailed logs)
        logging.debug(f"Task {task_id} worker completed.") # Renamed Job -> Task
    except Exception as exc:
        logging.error(f"Task {task_id} generated an exception: {exc}") # Renamed Job -> Task
    finally:
        with active_workers_lock:
            active_workers -= 1
            logging.info(f"Task {task_id} finished. Active workers: {active_workers}/{total_workers}") # Renamed Job -> Task

def _worker_init(shutdown_event_flag):
    """Initialize worker process to ignore SIGINT and set global shutdown flag."""
    signal.signal(signal.SIGINT, signal.SIG_IGN)
    # Make the shutdown event accessible within the worker process's scope
    # This is used by JobProcessor/TaskProcessor to check for shutdown signals
    global SHUTDOWN_EVENT
    SHUTDOWN_EVENT = shutdown_event_flag

def main():
    global active_workers

    # Setup signal handlers for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    logging.info("Worker starting up...") # Updated log

    with ProcessPoolExecutor(
        max_workers=total_workers,
        initializer=_worker_init,
        initargs=(shutdown_event,)
    ) as executor:
        futures = {}  # Track submitted futures {task_id: future}
        try:
            while not shutdown_event.is_set():
                try:
                    # Clean up completed futures first
                    completed_task_ids = {task_id for task_id, fut in futures.items() if fut.done()}
                    for task_id in completed_task_ids:
                        del futures[task_id]

                    # Only fetch new tasks if we have available workers
                    with active_workers_lock:
                        available_workers = total_workers - active_workers

                    # Choose task retrieval method based on configuration
                    if config.use_skip_locked:
                        # Try to get tasks one by one using SKIP LOCKED to prevent race conditions
                        while True:
                            with active_workers_lock:
                                if active_workers >= total_workers:
                                    break

                            # Get next available task atomically (already locked)
                            task = database.get_next_available_task()
                            if not task:
                                # No more tasks available
                                break

                            # Ensure task has all required fields for backward compatibility
                            if 'user_id' not in task:
                                task['user_id'] = None
                            if 'bulk_job_type' not in task:
                                task['bulk_job_type'] = None

                            task_id = task['id']

                            # Check if task is already running (shouldn't happen with SKIP LOCKED, but safety check)
                            if task_id in futures:
                                logging.warning(f"Task {task_id} already in futures despite SKIP LOCKED - this shouldn't happen")
                                continue

                            with active_workers_lock:
                                if active_workers < total_workers:
                                    active_workers += 1
                                    logging.info(f"Task {task_id} started. Active workers: {active_workers}/{total_workers}") # Renamed Job -> Task

                                    # Submit the task to the executor (task is already locked and status set to 'In Progress')
                                    future = executor.submit(run_task, task) # Renamed run_job -> run_task
                                    futures[task_id] = future  # Track the future by task_id

                                    # Add callback for when the task is done
                                    future.add_done_callback(
                                        lambda fut, tid=task_id: task_done_callback(fut, tid) # Renamed job_id -> tid, call task_done_callback
                                    )
                                else:
                                    # Max workers reached, break out of the loop
                                    break
                    else:
                        # Traditional method - fetch multiple tasks and try to lock them
                        if available_workers > 0:
                            # Limit task fetching to available workers to reduce race conditions
                            unprocessed_tasks = database.get_unprocessed_tasks(limit=available_workers * config.task_fetch_limit_multiplier)

                            for task in unprocessed_tasks:
                                task_id = task['id']
                                # Check if task is already running or recently completed
                                if task_id in futures:
                                    continue

                                if task['status'] == 'queued':
                                    with active_workers_lock:
                                        if active_workers < total_workers:
                                            active_workers += 1
                                            logging.info(f"Task {task_id} started. Active workers: {active_workers}/{total_workers}")

                                            # Submit the task to the executor
                                            future = executor.submit(run_task, task)
                                            futures[task_id] = future

                                            # Add callback for when the task is done
                                            future.add_done_callback(
                                                lambda fut, tid=task_id: task_done_callback(fut, tid)
                                            )
                                        else:
                                            # Max workers reached, break out of the loop
                                            break

                    # Adjust sleep time based on whether workers are busy
                    with active_workers_lock:
                        sleep_time = config.worker_sleep_time if active_workers == total_workers else config.worker_sleep_time
                    time.sleep(sleep_time)

                except Exception as e:
                    logging.error(f"Error in main loop: {e}", exc_info=True) # Added exc_info for better debugging
                    time.sleep(5) # Wait longer after an error

        except KeyboardInterrupt:
            logging.info("KeyboardInterrupt received. Initiating shutdown...")
            shutdown_event.set()
        
        finally:
             logging.info("Main loop exited. Waiting for active tasks to complete...")
             # Wait for all submitted tasks to complete
             # Note: executor shutdown happens automatically when exiting the 'with' block
             # concurrent.futures.wait(futures.values()) # Wait for all futures if needed, but shutdown handles this
             logging.info("All tasks completed or cancelled. Worker shutting down.")


if __name__ == "__main__":
    main()
